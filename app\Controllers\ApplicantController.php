<?php

namespace App\Controllers;

class ApplicantController extends BaseController
{
    protected $session;
    protected $applicantsModel;
    protected $experiencesModel;
    protected $educationModel;
    protected $educationLevelsModel;
    protected $filesModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'application']);
        $this->session = session();
        $this->applicantsModel = new \App\Models\ApplicantsModel();
        $this->experiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->educationModel = new \App\Models\ApplicantEducationModel();
        $this->educationLevelsModel = new \App\Models\EducationLevelsModel();
        $this->filesModel = new \App\Models\ApplicantFilesModel();
    }

    public function dashboard()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to access your dashboard');
        }

        // Get actual applicant data from database
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('applicant/login')->with('error', 'Applicant not found');
        }

        // Initialize models for dashboard data
        $applicationsModel = new \App\Models\AppxApplicationDetailsModel();
        $positionsModel = new \App\Models\PositionsModel();
        $exerciseModel = new \App\Models\ExerciseModel();
        $orgModel = new \App\Models\DakoiiOrgModel();
        $filesModel = new \App\Models\ApplicantFilesModel();

        // Get real application statistics for this applicant
        $all_applications = $applicationsModel->getApplicationsByApplicantId($applicant_id);
        $total_applications = count($all_applications);

        // Count applications by status
        $pending_applications = 0;
        $shortlisted_applications = 0;
        $rejected_applications = 0;

        foreach ($all_applications as $app) {
            $status = strtolower($app['application_status'] ?? 'pending');
            switch ($status) {
                case 'shortlisted':
                case 'shortlist':
                    $shortlisted_applications++;
                    break;
                case 'rejected':
                case 'reject':
                    $rejected_applications++;
                    break;
                default:
                    $pending_applications++;
                    break;
            }
        }

        // Get recent applications with position and organization details
        $recent_applications = [];
        $recent_apps = $applicationsModel->select('
                appx_application_details.*,
                positions.designation as position_title,
                dakoii_org.org_name as department
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->where('appx_application_details.applicant_id', $applicant_id)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->limit(5)
            ->findAll();

        foreach ($recent_apps as $app) {
            $recent_applications[] = [
                'id' => $app['id'],
                'position_title' => $app['position_title'] ?? 'Position Not Found',
                'department' => $app['department'] ?? 'Department Not Found',
                'created_at' => $app['created_at'],
                'application_status' => strtolower($app['application_status'] ?? 'pending')
            ];
        }

        // Get latest job openings from published exercises
        $latest_jobs = [];
        $published_positions = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.created_at as posted_date
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('(positions.status = 1 OR positions.status = "active")')
            ->where('exercises.status', 'published')
            ->orderBy('exercises.created_at', 'DESC')
            ->limit(5)
            ->findAll();

        foreach ($published_positions as $position) {
            $latest_jobs[] = [
                'id' => $position['exercise_id'],
                'title' => $position['designation'] ?? 'Position Title',
                'department' => $position['org_name'] ?? 'Department',
                'location' => $position['location'] ?? 'Location Not Specified',
                'posted_date' => $position['posted_date'] ?? date('Y-m-d')
            ];
        }

        // Get applicant's uploaded files count
        $files_count = $filesModel->where('applicant_id', $applicant_id)->countAllResults();

        return view('applicant/applicant_dashboard', [
            'title' => 'Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant,
            'total_applications' => $total_applications,
            'pending_applications' => $pending_applications,
            'shortlisted_applications' => $shortlisted_applications,
            'rejected_applications' => $rejected_applications,
            'recent_applications' => $recent_applications,
            'latest_jobs' => $latest_jobs,
            'files_count' => $files_count
        ]);
    }

    public function profile()
    {
        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to access your profile');
        }

        // Get applicant data from database
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('applicant/login')->with('error', 'Applicant not found');
        }

        // Get work experiences from database
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                             ->orderBy('date_from', 'DESC')
                                             ->findAll();

        // Get education records from database
        $education = $this->educationModel->where('applicant_id', $applicant_id)
                                         ->orderBy('date_from', 'DESC')
                                         ->findAll();

        // Get education levels from database
        $education_data = $this->educationLevelsModel->getActiveEducationLevels();

        // Create education levels array for backward compatibility
        $education_levels = [];
        foreach ($education_data as $level) {
            $education_levels[$level['id']] = $level['name'];
        }

        // Get applicant files from database
        $files = $this->filesModel->where('applicant_id', $applicant_id)
                                 ->orderBy('created_at', 'DESC')
                                 ->findAll();

        // Get organizations for dropdown
        $organizations = $this->applicantsModel->getOrganizationsForDropdown();

        return view('applicant/applicant_profile', [
            'title' => 'Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_levels' => $education_levels,
            'education_data' => $education_data,
            'files' => $files,
            'organizations' => $organizations
        ]);
    }

    // Personal Information Update
    public function updatePersonal()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'gender' => 'required|in_list[male,female]',
            'dobirth' => 'required|valid_date',
            'contact_details' => 'required|min_length[5]',
            'location_address' => 'permit_empty|max_length[500]',
            'place_of_origin' => 'permit_empty|max_length[255]',
            'citizenship' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'first_name' => trim($this->request->getPost('first_name')),
            'last_name' => trim($this->request->getPost('last_name')),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => trim($this->request->getPost('contact_details')),
            'location_address' => trim($this->request->getPost('location_address')),
            'place_of_origin' => trim($this->request->getPost('place_of_origin')),
            'citizenship' => trim($this->request->getPost('citizenship')),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);

            // Update session name
            session()->set('applicant_name', trim($data['first_name'] . ' ' . $data['last_name']));

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#personalInfo';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Personal information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating personal information: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error updating personal information. Please try again.');
        }
    }

    // Documents Update
    public function updateDocuments()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#documents')->with('success', 'Documents updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating documents: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating documents. Please try again.');
        }
    }

    // Employment Update
    public function updateEmployment()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Validation rules
        $validation = \Config\Services::validation();
        $rules = [
            'current_employer' => 'permit_empty|max_length[150]',
            'current_position' => 'permit_empty|max_length[150]',
            'current_salary' => 'permit_empty|decimal',
            'is_public_servant' => 'required|in_list[0,1]',
            'how_did_you_hear_about_us' => 'permit_empty|max_length[255]'
        ];

        // Add conditional validation for public service file number and organization
        $isPublicServant = $this->request->getPost('is_public_servant');
        if ($isPublicServant == '1') {
            $rules['public_service_file_number'] = 'required|max_length[20]';
            $rules['employee_of_org_id'] = 'permit_empty|integer';
        } else {
            $rules['public_service_file_number'] = 'permit_empty|max_length[20]';
            $rules['employee_of_org_id'] = 'permit_empty|integer';
        }

        $validation->setRules($rules);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'is_public_servant' => $isPublicServant,
            'public_service_file_number' => $isPublicServant == '1' ? $this->request->getPost('public_service_file_number') : null,
            'employee_of_org_id' => $isPublicServant == '1' && $this->request->getPost('employee_of_org_id') ? $this->request->getPost('employee_of_org_id') : null,
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#employment')->with('success', 'Employment information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating employment: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating employment information. Please try again.');
        }
    }

    // Add Experience
    public function addExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'employer' => trim($this->request->getPost('employer')),
            'position' => trim($this->request->getPost('position')),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => trim($this->request->getPost('employer_contacts_address')),
            'work_description' => trim($this->request->getPost('work_description')),
            'achievements' => trim($this->request->getPost('achievements')),
            'created_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->insert($data);

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#experiences';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Work experience added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding experience: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding work experience. Please try again.');
        }
    }

    // Update Experience
    public function updateExperience()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'employer' => 'required|min_length[2]|max_length[255]',
            'position' => 'required|min_length[2]|max_length[255]',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'employer_contacts_address' => 'permit_empty|max_length[500]',
            'work_description' => 'permit_empty|max_length[1000]',
            'achievements' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'employer' => trim($this->request->getPost('employer')),
            'position' => trim($this->request->getPost('position')),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'employer_contacts_address' => trim($this->request->getPost('employer_contacts_address')),
            'work_description' => trim($this->request->getPost('work_description')),
            'achievements' => trim($this->request->getPost('achievements')),
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->update($experience_id, $data);

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#experiences';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Work experience updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating work experience. Please try again.');
        }
    }

    // Delete Experience
    public function deleteExperience($experience_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return redirect()->back()->with('error', 'Experience not found or access denied');
        }

        try {
            $this->experiencesModel->delete($experience_id);

            // Handle scroll position restoration
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#experiences';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Work experience deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting experience: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting work experience. Please try again.');
        }
    }

    // Add Education
    public function addEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'applicant_id' => $applicant_id,
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'created_by' => $applicant_id
        ];

        try {
            $this->educationModel->insert($data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record added successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error adding education: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error adding education record. Please try again.');
        }
    }

    // Update Education
    public function updateEducation()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'institution' => 'required|min_length[2]|max_length[255]',
            'course' => 'required|min_length[2]|max_length[255]',
            'education_level' => 'required|numeric',
            'date_from' => 'required|valid_date',
            'date_to' => 'permit_empty|valid_date',
            'units' => 'permit_empty|max_length[1000]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'institution' => $this->request->getPost('institution'),
            'course' => $this->request->getPost('course'),
            'education_level' => $this->request->getPost('education_level'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to') ?: null,
            'units' => $this->request->getPost('units'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->educationModel->update($education_id, $data);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating education record. Please try again.');
        }
    }

    // Delete Education
    public function deleteEducation($education_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $education = $this->educationModel->where('id', $education_id)
                                        ->where('applicant_id', $applicant_id)
                                        ->first();

        if (!$education) {
            return redirect()->back()->with('error', 'Education record not found or access denied');
        }

        try {
            $this->educationModel->delete($education_id);
            return redirect()->to('applicant/profile#education')->with('success', 'Education record deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting education: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting education record. Please try again.');
        }
    }

    // Update Family Information
    public function updateFamily()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Process children data
        $children_data = $this->request->getPost('children');
        $children_json = null;

        if ($children_data && is_array($children_data)) {
            // Filter out empty children entries
            $filtered_children = array_filter($children_data, function($child) {
                return !empty($child['name']) || !empty($child['dob']) || !empty($child['gender']);
            });

            if (!empty($filtered_children)) {
                $children_json = json_encode(array_values($filtered_children));
            }
        }

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage') ?: null,
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => $children_json,
            'updated_by' => $applicant_id
        ];

        // All fields are valid according to the model's allowedFields, so no filtering needed

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#family')->with('success', 'Family information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating family information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating family information. Please try again.');
        }
    }

    // Update Additional Information
    public function updateAdditional()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);

            // Get scroll position from session if available
            $scrollPosition = $this->request->getPost('scroll_position');
            $redirectUrl = 'applicant/profile#additional';
            if ($scrollPosition) {
                $redirectUrl .= '?scroll=' . $scrollPosition;
            }

            return redirect()->to($redirectUrl)->with('success', 'Additional information updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating additional information: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating additional information. Please try again.');
        }
    }

    // Show create file form
    public function createFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $data = [
            'title' => 'Upload New File',
            'menu' => 'profile'
        ];

        return view('applicant/applicant_files_create', $data);
    }

    // Show edit file form
    public function editFile($file_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership and get file details
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->to('applicant/profile#files')->with('error', 'File not found or access denied');
        }

        $data = [
            'title' => 'Edit File Information',
            'menu' => 'profile',
            'file' => $file
        ];

        return view('applicant/applicant_files_edit', $data);
    }

    // Upload File
    public function uploadFile()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Debug: Log the request data
        log_message('debug', 'Upload file request - Applicant ID: ' . $applicant_id);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]',
            'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf,doc,docx,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            $errors = $validation->getErrors();
            log_message('error', 'File upload validation failed: ' . json_encode($errors));
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Check if we have extracted text data (new workflow) or file upload (legacy)
        $extractedText = $this->request->getPost('extracted_text');
        $fileData = $this->request->getPost('file_data');

        if ($extractedText && $fileData) {
            // New workflow: File was processed client-side with AI
            log_message('info', 'Processing file upload with pre-extracted text');

            $fileInfo = json_decode($fileData, true);

            // Validate that we have the required data
            if (!$fileInfo || !isset($fileInfo['name']) || !isset($fileInfo['size'])) {
                return redirect()->back()->withInput()->with('error', 'Invalid file data. Please try again.');
            }

            // Still need to get the actual file for upload
            $file = $this->request->getFile('file');
            if (!$file || !$file->isValid()) {
                return redirect()->back()->withInput()->with('error', 'Please select a valid PDF file');
            }

            // Check file type - only PDF allowed for new workflow
            if ($file->getMimeType() !== 'application/pdf') {
                return redirect()->back()->withInput()->with('error', 'Invalid file type. Only PDF files are allowed');
            }

            log_message('info', 'New workflow: File ' . $fileInfo['name'] . ' with ' . strlen($extractedText) . ' characters of extracted text');

        } else {
            // Legacy workflow: Direct file upload
            log_message('info', 'Processing file upload with server-side text extraction');

            $file = $this->request->getFile('file');

            // Debug: Log file information
            if ($file) {
                log_message('debug', 'File info - Name: ' . $file->getName() . ', Size: ' . $file->getSize() . ', Type: ' . $file->getMimeType());
                log_message('debug', 'File valid: ' . ($file->isValid() ? 'Yes' : 'No'));
                log_message('debug', 'File moved: ' . ($file->hasMoved() ? 'Yes' : 'No'));
                if (!$file->isValid()) {
                    log_message('error', 'File error: ' . $file->getErrorString());
                }
            } else {
                log_message('error', 'No file received in request');
            }
        }

        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Get file information BEFORE moving the file
            $originalName = $file->getName();
            $newName = $file->getRandomName();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Try to get MIME type safely
            $mimeType = null;
            try {
                $mimeType = $file->getMimeType();
            } catch (\Exception $e) {
                log_message('warning', 'Could not get MIME type from uploaded file: ' . $e->getMessage());
                // Will be determined later from file extension
            }

            // Debug: Log upload path and file info
            log_message('debug', 'Upload path: ' . $uploadPath);
            log_message('debug', 'Original file: ' . $originalName . ', MIME: ' . ($mimeType ?: 'unknown'));

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                if (!mkdir($uploadPath, 0755, true)) {
                    log_message('error', 'Failed to create upload directory: ' . $uploadPath);
                    return redirect()->back()->with('error', 'Error creating upload directory. Please try again.');
                }
                log_message('debug', 'Created upload directory: ' . $uploadPath);
            }

            // Check if directory is writable
            if (!is_writable($uploadPath)) {
                log_message('error', 'Upload directory is not writable: ' . $uploadPath);
                return redirect()->back()->with('error', 'Upload directory is not writable. Please contact administrator.');
            }

            if ($file->move($uploadPath, $newName)) {
                log_message('debug', 'File moved successfully to: ' . $uploadPath . $newName);

                // Get file path for Gemini AI
                $filePath = $uploadPath . $newName;

                // Handle text extraction based on workflow
                $finalExtractedText = null;
                $extractionStatus = 'failed';

                if ($this->request->getPost('extracted_text')) {
                    // Use pre-extracted text from client-side processing
                    $finalExtractedText = $this->request->getPost('extracted_text');
                    $extractionStatus = 'completed';
                    log_message('info', 'Using pre-extracted text for file: ' . $newName . ' - ' . strlen($finalExtractedText) . ' characters');
                } else {
                    // Load the Gemini helper for server-side extraction
                    helper('GeminiAI');

                    // Extract text using simple Gemini function
                    log_message('info', "Starting server-side text extraction for file: {$newName}");
                    $extractionResult = gemini_extract_text_from_file($filePath);

                    if ($extractionResult['success']) {
                        $finalExtractedText = $extractionResult['extracted_text'];
                        $extractionStatus = 'completed';
                        log_message('info', 'Text extraction successful for file: ' . $newName . ' - ' . strlen($finalExtractedText) . ' characters extracted');
                    } else {
                        log_message('error', 'Text extraction failed for file: ' . $newName . ' - ' . $extractionResult['message']);
                    }
                }

                $data = [
                    'applicant_id' => $applicant_id,
                    'file_title' => $this->request->getPost('file_title'),
                    'file_description' => $this->request->getPost('file_description'),
                    'file_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'file_extracted_texts' => $finalExtractedText,
                    'created_by' => $applicant_id
                ];

                // Debug: Log data to be inserted
                log_message('debug', 'Data to insert: ' . json_encode(array_merge($data, ['file_extracted_texts' => $finalExtractedText ? substr($finalExtractedText, 0, 100) . '...' : null])));

                try {
                    $result = $this->filesModel->insert($data);
                    log_message('debug', 'File record inserted with ID: ' . $result);

                    // Set success message
                    if ($extractionStatus === 'completed') {
                        if ($this->request->getPost('extracted_text')) {
                            $successMessage = 'PDF uploaded successfully with AI-extracted text!';
                        } else {
                            $successMessage = 'File uploaded and text extracted successfully!';
                        }
                    } else {
                        $successMessage = 'File uploaded successfully, but text extraction failed.';
                    }

                    // Determine redirect URL based on the route used
                    $currentRoute = $this->request->getUri()->getPath();
                    if (strpos($currentRoute, 'files/store') !== false) {
                        // New RESTful route - redirect to profile files section
                        return redirect()->to('applicant/profile#files')->with('success', $successMessage);
                    } else {
                        // Legacy route - handle scroll position restoration
                        $scrollPosition = $this->request->getPost('scroll_position');
                        $redirectUrl = 'applicant/profile#files';
                        if ($scrollPosition) {
                            $redirectUrl .= '?scroll=' . $scrollPosition;
                        }
                        return redirect()->to($redirectUrl)->with('success', $successMessage);
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Error saving file record: ' . $e->getMessage());
                    // Delete the uploaded file if database insert fails
                    if (file_exists($uploadPath . $newName)) {
                        unlink($uploadPath . $newName);
                    }
                    return redirect()->back()->with('error', 'Error saving file record. Please try again.');
                }
            } else {
                log_message('error', 'Failed to move file to: ' . $uploadPath . $newName);
                return redirect()->back()->with('error', 'Error uploading file. Please try again.');
            }
        } else {
            $errorMsg = 'Invalid file or file upload error.';
            if ($file) {
                $errorMsg .= ' Error: ' . $file->getErrorString();
            }
            log_message('error', $errorMsg);
            return redirect()->back()->with('error', $errorMsg);
        }
    }

    // Update File
    public function updateFile($file_id = null)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Get file_id from URL parameter (RESTful) or POST data (legacy)
        if ($file_id === null) {
            $file_id = $this->request->getPost('file_id');
        }

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty|max_length[500]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->filesModel->update($file_id, $data);

            // Determine redirect URL based on the route used
            $currentRoute = $this->request->getUri()->getPath();
            if (strpos($currentRoute, 'files/' . $file_id . '/update') !== false) {
                // New RESTful route - redirect to profile files section
                return redirect()->to('applicant/profile#files')->with('success', 'File information updated successfully');
            } else {
                // Legacy route - handle scroll position restoration
                $scrollPosition = $this->request->getPost('scroll_position');
                $redirectUrl = 'applicant/profile#files';
                if ($scrollPosition) {
                    $redirectUrl .= '?scroll=' . $scrollPosition;
                }
                return redirect()->to($redirectUrl)->with('success', 'File information updated successfully');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating file information. Please try again.');
        }
    }

    // Delete File
    public function deleteFile($file_id)
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        // Verify ownership
        $file = $this->filesModel->where('id', $file_id)
                                ->where('applicant_id', $applicant_id)
                                ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'File not found or access denied');
        }

        try {
            // Delete physical file (remove 'public/' prefix for file system path)
            $physicalPath = str_replace('public/', FCPATH, $file['file_path']);
            if (file_exists($physicalPath)) {
                unlink($physicalPath);
            }

            // Delete database record
            $this->filesModel->delete($file_id);

            // Handle scroll position restoration (from localStorage since this is called via JS)
            return redirect()->to('applicant/profile#files')->with('success', 'File deleted successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error deleting file: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting file. Please try again.');
        }
    }

    // Change Password
    public function changePassword()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'current_password' => 'required',
            'new_password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[new_password]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found');
        }

        // Verify current password
        if (!password_verify($this->request->getPost('current_password'), $applicant['password'])) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        $data = [
            'password' => $this->request->getPost('new_password'), // Will be hashed by model callback
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#security')->with('success', 'Password changed successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error changing password: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error changing password. Please try again.');
        }
    }

    // Check Email Availability for Update
    public function checkEmailAvailabilityForUpdate()
    {
        // Only allow AJAX requests
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        $applicant_id = session()->get('applicant_id');
        if (!$applicant_id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please login to continue'
            ]);
        }

        $email = $this->request->getPost('email');

        if (!$email) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Email is required'
            ]);
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid email format'
            ]);
        }

        // Check if email exists for other applicants (exclude current applicant)
        $existingApplicant = $this->applicantsModel->where('email', $email)
                                                  ->where('id !=', $applicant_id)
                                                  ->first();

        if ($existingApplicant) {
            return $this->response->setJSON([
                'success' => false,
                'available' => false,
                'message' => 'This email address is already registered'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => true,
                'available' => true,
                'message' => 'Email address is available'
            ]);
        }
    }

    // Update Email Address
    public function updateEmail()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'new_email' => 'required|valid_email|max_length[255]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $newEmail = $this->request->getPost('new_email');

        // Check if email exists for other applicants (exclude current applicant)
        $existingApplicant = $this->applicantsModel->where('email', $newEmail)
                                                  ->where('id !=', $applicant_id)
                                                  ->first();

        if ($existingApplicant) {
            return redirect()->back()->with('error', 'This email address is already registered');
        }

        $data = [
            'email' => $newEmail,
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return redirect()->to('applicant/profile#personalInfo')->with('success', 'Email address updated successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error updating email address: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating email address. Please try again.');
        }
    }

    // Upload Photo
    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');

        if (!$applicant_id) {
            return redirect()->to('applicant/login')->with('error', 'Please login to continue');
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'id_photo' => 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->with('errors', $validation->getErrors());
        }

        $file = $this->request->getFile('id_photo');

        if ($file->isValid() && !$file->hasMoved()) {
            $newName = 'photo_' . $applicant_id . '_' . time() . '.' . $file->getExtension();
            $uploadPath = FCPATH . 'uploads/applicants/' . $applicant_id . '/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if ($file->move($uploadPath, $newName)) {
                $data = [
                    'id_photo_path' => 'public/uploads/applicants/' . $applicant_id . '/' . $newName,
                    'updated_by' => $applicant_id
                ];

                try {
                    $this->applicantsModel->update($applicant_id, $data);
                    return redirect()->to('applicant/profile')->with('success', 'Profile photo updated successfully');
                } catch (\Exception $e) {
                    log_message('error', 'Error updating photo: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error updating profile photo. Please try again.');
                }
            } else {
                return redirect()->back()->with('error', 'Error uploading photo. Please try again.');
            }
        } else {
            return redirect()->back()->with('error', 'Invalid photo file or upload error.');
        }
    }







    // Note: serveFile method removed since files are now stored in public directory
    // and can be accessed directly via base_url()
}